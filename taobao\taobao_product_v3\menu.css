/* 对应修改CSS选择器，提高特异性 */
.extension-switch {
  position: relative;
  display: inline-block;
  width: 60px;
  height: 34px;
  z-index: 9999; /* 确保显示在页面元素之上 */
}

.extension-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.extension-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  -webkit-transition: .4s;
  transition: .4s;
  z-index: 9999; /* 确保显示在页面元素之上 */
}

.extension-slider:before {
  position: absolute;
  content: "";
  height: 26px;
  width: 26px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  -webkit-transition: .4s;
  transition: .4s;
}

input:checked + .extension-slider {
  background-color: #2196F3;
}

input:focus + .extension-slider {
  box-shadow: 0 0 1px #2196F3;
}

input:checked + .extension-slider:before {
  -webkit-transform: translateX(26px);
  -ms-transform: translateX(26px);
  transform: translateX(26px);
}

.extension-round {
  border-radius: 34px;
}

.extension-round:before {
  border-radius: 50%;
} 