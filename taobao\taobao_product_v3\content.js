let enableLogging = true;
// 添加调试级别日志记录
function logDebug(message, data) {
	if (!enableLogging) return;
	if (data !== undefined) {
		console.log(`[DEBUG] ${message}`, data);
	} else {
		console.log(`[DEBUG] ${message}`);
	}
}
///////////////////////////////////////////////////////////////////    动态参数区  //////////////////////////////////////////////////////////////////////////////////////
const taobao_key = 'taobao_search_items_';
const filename_prefix = 'taobao_relang_data';


const CHANNELS = {
	SEARCH: {
		id: 'search',
		displayName: '热浪联盟搜索数据'
	},
	ALL_PRODUCTS: {
		id: 'all_products',
		displayName: '热浪联盟全部商品'
	}
};

// 获取所有渠道ID
const sources = Object.values(CHANNELS).map(channel => channel.id);
// 建立渠道ID到显示名称的映射
const sourceDisplayNames = {};
Object.values(CHANNELS).forEach(channel => {
	sourceDisplayNames[channel.id] = channel.displayName;
});

////////////////////////////////////////////////////////////////////////////////   内容脚本 - 页面加载时自动注入   ////////////////////////////////////////////////////////////////////////////////

(function () {
	logDebug("[热浪联盟数据采集器] 内容脚本已加载...");

	// 获取JSONP监控脚本的URL
	const jsonpScriptURL = chrome.runtime.getURL('xhr-monitor.js');

	// 创建JSONP监控script元素并设置src
	const jsonpScriptElement = document.createElement('script');
	jsonpScriptElement.src = jsonpScriptURL;
	jsonpScriptElement.onload = function () {
		// 脚本加载完成后可以移除
		this.remove();
		logDebug("[热浪联盟监听器] 监控脚本已加载");
	};

	// 将脚本添加到文档
	(document.head || document.documentElement).appendChild(jsonpScriptElement);
})();

$(document).ready(function () {
	showMenu();
});

function showMenu() {
	logDebug("尝试加载menu.html");

	$.ajax({
		url: chrome.runtime.getURL('menu.html'),
		type: 'GET',
		dataType: 'html',
		success: function (html) {
			logDebug("menu.html成功加载");

			$('body').append(html);
			updateDataCount();

			// 设置面板位置
			const savedPosition = JSON.parse(localStorage.getItem('draggableDivPosition'));
			if (savedPosition) {
				const draggableDiv = document.getElementById('draggableDiv');
				draggableDiv.style.top = savedPosition.top;
				draggableDiv.style.left = savedPosition.left;
			}

			// 初始化所有渠道的开关状态
			initSwitchStates();

			function dragElement(elmnt) {
				var pos1 = 0,
					pos2 = 0,
					pos3 = 0,
					pos4 = 0;

				// 只在面板头部允许拖拽
				const panelHeader = document.querySelector('.panel-header');
				if (panelHeader) {
					panelHeader.onmousedown = dragMouseDown;
				} else {
					// 如果没有找到标题栏，则整个面板可拖拽（作为备选方案）
					elmnt.onmousedown = dragMouseDown;
				}

				function dragMouseDown(e) {
					e = e || window.event;
					// 阻止点击在输入框上时触发拖拽
					if (e.target.tagName === 'INPUT') {
						return;
					}
					e.preventDefault();
					// Get the mouse cursor position at startup:
					pos3 = e.clientX;
					pos4 = e.clientY;
					document.onmouseup = closeDragElement;
					// Call a function whenever the cursor moves:
					document.onmousemove = elementDrag;
				}

				function elementDrag(e) {
					e = e || window.event;
					e.preventDefault();
					// Calculate the new cursor position:
					pos1 = pos3 - e.clientX;
					pos2 = pos4 - e.clientY;
					pos3 = e.clientX;
					pos4 = e.clientY;
					// Set the element's new position:
					elmnt.style.top = (elmnt.offsetTop - pos2) + "px";
					elmnt.style.left = (elmnt.offsetLeft - pos1) + "px";
				}

				function closeDragElement() {
					// Stop moving when mouse button is released:
					document.onmouseup = null;
					document.onmousemove = null;

					// Save the new position to localStorage
					const newPosition = {
						top: elmnt.style.top,
						left: elmnt.style.left
					};
					localStorage.setItem('draggableDivPosition', JSON.stringify(newPosition));
				}
			}

			dragElement(document.getElementById("draggableDiv"));
			// 初始化佣金值
			initCommissionValues();
			// 启用导出和删除按钮的监听器
			$('body').on('click', '#remove-button', removeData);
			$('body').on('click', '#export-button', exportData);
			// 监听佣金值变化
			$('#min-commission, #max-commission').on('input', function () {
				saveCommissionValues();
			});
		},
		error: function (xhr, status, error) {
			logDebug('加载menu.html失败', status, error);
		}
	});
}
//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// 初始化佣金值
function initCommissionValues() {
	const savedMinCommission = localStorage.getItem('minCommission') || '0.1';
	const savedMaxCommission = localStorage.getItem('maxCommission') || '9999';

	$('#min-commission').val(savedMinCommission);
	$('#max-commission').val(savedMaxCommission);

	// 初始化后立即发送到背景脚本
	chrome.runtime.sendMessage({
		action: 'updateCommissionRange',
		data: {
			minCommission: savedMinCommission,
			maxCommission: savedMaxCommission
		}
	}, function (response) {
		if (response && response.success) {
			logDebug('初始化时佣金范围已同步到背景脚本');
		}
	});
}
// 保存佣金值变动
function saveCommissionValues() {
	const minCommission = $('#min-commission').val() || '0.1';
	const maxCommission = $('#max-commission').val() || '9999';

	// 保存到本地存储
	localStorage.setItem('minCommission', minCommission);
	localStorage.setItem('maxCommission', maxCommission);

	// 发送到背景脚本
	chrome.runtime.sendMessage({
		action: 'updateCommissionRange',
		data: {
			minCommission: minCommission,
			maxCommission: maxCommission
		}
	}, function (response) {
		if (response && response.success) {
			logDebug('背景脚本已更新佣金范围');
		} else {
			console.error('无法更新背景脚本的佣金范围');
		}
	});

	logDebug('佣金范围已更新:', { min: minCommission, max: maxCommission });
}

// 将updateDataCount函数修改为显示当前渠道的数据量
function updateDataCount() {

	chrome.runtime.sendMessage({
		action: "getDataCount",
		sources: sources
	}, function (response) {
		if (response && response.success) {

			let totalCount = response.counts;
			const text = `<span style="color: green;">成功采集: ${totalCount}</span>`;
			$('#data-num').html(text);
		}
	});
}




// 初始化所有渠道的开关状态
function initSwitchStates() {

	// 获取开关元素
	const switchElement = $('#automatic-switch');

	// 从 chrome.storage.local 加载当前渠道的开关状态
	const sourceKey = `automaticEnabled`;

	chrome.storage.local.get(sourceKey, function (result) {
		// 默认为开启状态
		const isEnabled = result[sourceKey] !== false;
		switchElement.prop('checked', isEnabled);



		// 无论之前是否存在状态，都主动向背景脚本发送一次当前状态
		// 确保背景脚本有这个渠道的初始状态
		chrome.runtime.sendMessage({
			type: 'toggleAutomaticMode',
			enabled: isEnabled
		}, function (response) {
			if (response && response.success) {
				logDebug(`初始化开关状态为: ${isEnabled ? '开启' : '关闭'}`);
			} else {
				logDebug(`无法初始化的开关状态`);
			}
		});

		// 监听开关状态变化
		switchElement.off('change').on('change', function () {
			const isEnabled = switchElement.is(':checked');

			// 通知背景脚本开关状态已变化
			chrome.runtime.sendMessage({
				type: 'toggleAutomaticMode',
				enabled: isEnabled
			}, function (response) {
				if (response && response.success) {
					logDebug(`已更新的开关状态为: ${isEnabled ? '开启' : '关闭'}`);
				} else {
					logDebug(`无法更新的开关状态`);
				}
			});
		});
	});
}

/**
 * 导出数据函数
 */
function exportData() {
	try {

		// 直接从后台脚本获取所有标签页的数据
		layer.msg('正在获取所有数据...', { icon: 16, time: 0 });
		chrome.runtime.sendMessage({
			action: "getAllData",
			sources: sources
		}, function (response) {
			layer.closeAll();
			if (response && response.success) {
				const allSourcesData = [];

				// 处理从后台获取的数据
				for (const source in response.data) {
					if (response.data[source] && response.data[source].length > 0) {
						allSourcesData.push({
							source: source,
							data: response.data[source]
						});
					}
				}

				if (allSourcesData.length === 0) {
					layer.msg('所有渠道均无数据');
					return;
				}

				// 导出Excel
				exportToExcel(allSourcesData);
			} else {
				layer.msg('获取数据失败，请查看控制台');
				logDebug('获取数据失败:', response ? response.error : '无响应');
			}
		});

	} catch (e) {
		logDebug('导出数据失败:', e);
		layer.msg('导出数据失败，请查看控制台');
	}
}


/**
 * 获取数据源的显示名称
 * @param {string} source - 数据源名称
 * @returns {string} 显示名称
 */
function getSourceDisplayName(source) {
	return sourceDisplayNames[source] || source;
}

/**
 * 导出数据为Excel
 * @param {Array} sourcesData - 要导出的数据，格式为[{source, data}, ...]
 */
function exportToExcel(sourcesData) {
	try {
		// 创建工作簿
		const wb = XLSX.utils.book_new();
		let totalItems = 0;

		// 为每个数据源创建单独的工作表
		sourcesData.forEach(sourceData => {
			// 准备当前数据源的Excel数据
			const source = sourceData.source;
			const data = sourceData.data;

			// 根据渠道类型设置不同的表头
			let headers, colWidths, getRowData;

			if (source === 'search') {
				// 搜索渠道的表头和数据处理
				headers = ['商品ID', '商品标题', '价格', '佣金率', '佣金金额', '淘宝链接', '店名', '采集时间'];
				colWidths = [
					{ wch: 20 }, // 商品ID
					{ wch: 40 }, // 商品标题
					{ wch: 15 }, // 价格
					{ wch: 15 }, // 佣金率  
					{ wch: 15 }, // 佣金金额
					{ wch: 50 }, // 淘宝链接
					{ wch: 30 }, // 店名
					{ wch: 20 }  // 采集时间
				];
				getRowData = (item) => [
					String(item.itemId) || '', // 转换为字符串
					item.title || '',
					item.price || '',
					item.commisionRatio || '',
					item.commision || '',
					`https://item.taobao.com/item.htm?id=${item.itemId}`,
					item.shopName || '',
					new Date(item.timestamp).toLocaleString()
				];
			} else if (source === 'all_products') {
				// 全部商品渠道的表头和数据处理
				headers = ['商品ID', '淘宝链接','商品名称', '商品价格', '佣金', '30天销量', '7天销量', '1天销量', 'TCP佣金','类型', '销量增长率', '30天订单数', '特征标签', '采集时间'];
				colWidths = [
					{ wch: 20 }, // 商品ID
					{ wch: 40 }, // 淘宝链接
					{ wch: 40 }, // 商品名称
					{ wch: 15 }, // 商品价格
					{ wch: 15 }, // 佣金
					{ wch: 15 }, // 30天销量
					{ wch: 15 }, // 7天销量
					{ wch: 15 }, // 1天销量
					{ wch: 15 }, // TCP佣金
					{ wch: 15 }, // 销量增长率
					{ wch: 15 }, // 30天订单数
					{ wch: 30 }, // 特征标签
					{ wch: 20 }  // 采集时间
				];
				getRowData = (item) => [
					String(item.itemId) || '',
					`https://item.taobao.com/item.htm?id=${item.itemId}`,
					item.itemName || '',
					item.itemPrice || '',
					item.commission || '',
					item.soldQuantity30 || '',
					item.soldQuantityLive7d || '',
					item.soldQuantityLive1d || '',
					item.tcpCommission || '',
					item.tcpCommissionType || '',
					item.sales_rise_compare_last_week || '',
					item.placedOrdersIn30 || '',
					Array.isArray(item.featureTags) ? item.featureTags.join(', ') : '',
					new Date(item.timestamp).toLocaleString()
				];
			} else {
				// 默认表头（兼容其他可能的渠道）
				headers = ['商品ID', '商品标题', '价格', '佣金率', '佣金金额', '淘宝链接', '店名', '采集时间'];
				colWidths = [
					{ wch: 20 }, // 商品ID
					{ wch: 40 }, // 商品标题
					{ wch: 15 }, // 价格
					{ wch: 15 }, // 佣金率  
					{ wch: 15 }, // 佣金金额
					{ wch: 50 }, // 淘宝链接
					{ wch: 30 }, // 店名
					{ wch: 20 }  // 采集时间
				];
				getRowData = (item) => [
					String(item.itemId) || '',
					item.title || item.itemName || '',
					item.price || item.itemPrice || '',
					item.commisionRatio || '',
					item.commision || item.commission || '',
					`https://detail.tmall.com/item.htm?id=${item.itemId}`,
					item.shopName || '',
					new Date(item.timestamp).toLocaleString()
				];
			}

			const sheetData = [headers];

			// 添加数据行
			data.forEach(item => {
				const row = getRowData(item);
				sheetData.push(row);
				totalItems++;
			});

			// 创建工作表
			const ws = XLSX.utils.aoa_to_sheet(sheetData);

			// 设置列宽
			ws['!cols'] = colWidths;

			// 添加工作表到工作簿
			const sheetName = getSourceDisplayName(source);
			XLSX.utils.book_append_sheet(wb, ws, sheetName);
		});

		// 生成文件名
		const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
		const filename = `${filename_prefix}_${timestamp}.xlsx`;

		// 导出文件
		XLSX.writeFile(wb, filename);

		// 显示成功消息
		layer.msg(`成功导出${totalItems}条数据`);
		logDebug(`成功导出数据到${filename}`);
	} catch (e) {
		logDebug('导出Excel失败:', e);
		layer.msg('导出数据失败，请查看控制台');
	}
}

/**
 * 删除数据函数
 * 清除当前页面采集的数据或所有数据
 */
function removeData() {
	try {

		// 弹出选择删除范围的对话框
		layer.confirm('确定要删除所有数据？', {
			title: '删除数据',
			btn: ['删除', '取消']
		}, function (index) {
			// 删除所有数据
			removeAllData();
			layer.close(index);
		});
	} catch (e) {
		logDebug('删除数据失败:', e);
		layer.msg('删除数据失败，请查看控制台');
	}
}


/**
 * 删除所有数据
 */
function removeAllData() {
	try {
		// 使用消息机制通知后台脚本清除所有数据
		layer.msg('正在清除所有数据...', { icon: 16, time: 0 });
		chrome.runtime.sendMessage({
			action: "clearAllData",
			sources: sources
		}, function (response) {
			layer.closeAll();
			if (response && response.success) {
				// 更新显示
				updateDataCount();

				// 显示成功消息
				layer.msg(`已清除所有数据`);
			} else {
				layer.msg('清除数据失败，请查看控制台');
				logDebug('清除数据失败:', response ? response.error : '无响应');
			}
		});
	} catch (error) {
		logDebug('删除操作失败:', error);
		layer.closeAll();
		layer.msg('操作失败，请刷新页面后重试');
	}

}



// 监听来自后台脚本的消息
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
	if (message.action === "updateDataCount") {
		logDebug(`[前端] 收到数据计数更新消息:`, message.counts);
		updateDisplayCount(message.counts);
		sendResponse({ success: true });
	}
	return true; // 保持消息通道打开
});

function updateDisplayCount(counts) {
	const text = `<span style="color: green;">成功采集: ${counts}</span>`;
	$('#data-num').html(text);
}

// 修改现有的消息监听器，检查渠道状态后再发送数据
window.addEventListener('message', function (event) {
	// 确保消息来源于当前窗口
	if (event.source !== window) return;
	const data = event.data;
	// 检查消息类型
	if (data && data.type === 'ITEM_DATA') {
		logDebug('[接收数据] 从XHR监控接收到数据:', event.data);

		try {
			const items = data.response;
			// 处理不同类型的数据
			if (Array.isArray(items)) {
				const source = data.channel;
				// 将数据发送到背景脚本，背景脚本会检查开关状态
				chrome.runtime.sendMessage({
					action: "saveData",
					source: source,
					items: items
				}).catch(error => {
					logDebug('发送数据到背景脚本失败:', error);
				});

				logDebug(`[${source}] 成功发送${items.length}条数据到背景脚本`);

			}
		} catch (e) {
			logDebug('处理接收到的数据失败:', e);
		}
	}
});
