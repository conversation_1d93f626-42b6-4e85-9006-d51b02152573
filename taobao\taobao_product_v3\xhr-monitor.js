// 热浪联盟API请求监控脚本 - 多渠道版本
(function () {
    // 日志控制变量
    let enableLogging = true;
    
    // 添加调试级别日志记录
    function logDebug(message, data) {
        if (!enableLogging) return;
        if (data !== undefined) {
            console.log(`[DEBUG] ${message}`, data);
        } else {
            console.log(`[DEBUG] ${message}`);
        }
    }

    // 渠道常量
    const CHANNELS = {
        SEARCH: 'search',
        ALL_PRODUCTS: 'all_products'  // 新增全部商品渠道
    };

    // 目标API配置
    const TARGET_APIS = [
        {
            url: 'mtop.taobao.cic.downlink.newstation.itemcenter.item.query',
            channel: CHANNELS.ALL_PRODUCTS,
            processor: 'processAllProductsData'
        }
    ];

    logDebug('[多渠道商品监控] 开始初始化');
    
    // 保存已处理的URL和回调
    const processedUrls = new Set();
    const processedCallbacks = new Set();

    // ==================== 数据处理器 ====================
    
    // 数据处理器集合
    const dataProcessors = {
        // 搜索渠道数据处理器（原有逻辑）
        processSearchData: function(data) {
            try {
                if (!data || !data.result) {
                    return [];
                }
                
                const items = data.result;
                logDebug(`[搜索渠道] 筛选后剩余 ${items.length} 条商品数据`);
                
                const parsedData = items.map(item => {
                    return {
                        itemId: item.skuId || '',
                        title: item.skuName || '',
                        price: item.priceInfo.lowestCouponPrice || 0,
                        commision: item.commissionInfo.commission || 0,
                        commisionRatio: item.commissionInfo.commissionShare || 0,
                        commission: item.commissionInfo.commission || 0,
                        shopName: item.shopInfo.shopName || ''
                    };
                });

                logDebug(`[搜索渠道] 成功提取 ${parsedData.length} 条商品数据`);
                return parsedData;
            } catch (e) {
                logDebug('[搜索渠道] 解析失败', e);
                return [];
            }
        },

        // 全部商品渠道数据处理器
        processAllProductsData: function(data) {
            try {
                if (!data || !data.data || !data.data.data || !data.data.data.list) {
                    logDebug('[全部商品渠道] 数据结构异常', data);
                    return [];
                }
                
                const items = data.data.data.list;
                logDebug(`[全部商品渠道] 获取到 ${items.length} 条商品数据`);
                
                const parsedData = items.map(item => {
                    const extendVal = item.extendVal || {};
                    
                    return {
                        itemId: item.itemId || '',
                        itemName: item.itemName || '',
                        itemPrice: item.itemPrice || '',
                        commission: extendVal.commissionPrice || '',
                        soldQuantity30: extendVal.soldQuantity30 || item.soldQuantity || '',
                        soldQuantityLive7d: extendVal.soldQuantityLive7d || '',
                        soldQuantityLive1d: extendVal.soldQuantityLive1d || '',
                        tcpCommission: extendVal.tcpCommission || '',
                        tcpCommissionType: extendVal.tcpCommissionType || '',
                        sales_rise_compare_last_week: extendVal.sales_rise_compare_last_week || '',
                        placedOrdersIn30: extendVal.placedOrdersIn30 || '',
                        featureTags: item.featureTags || []
                    };
                });

                logDebug(`[全部商品渠道] 成功提取 ${parsedData.length} 条商品数据`);
                return parsedData;
            } catch (e) {
                logDebug('[全部商品渠道] 解析失败', e);
                return [];
            }
        }
    };

    // ==================== 公用函数 ====================
    
    // 通用JSONP响应处理函数
    function processJsonpResponse(data, url) {
        try {
            // 根据URL确定渠道和处理器
            const targetApi = TARGET_APIS.find(api => url.includes(api.url));
            if (!targetApi) {
                logDebug('[JSONP响应] 未找到匹配的API配置', url);
                return;
            }

            const channel = targetApi.channel;
            const processorName = targetApi.processor;
            const processor = dataProcessors[processorName];
            
            if (!processor) {
                logDebug('[JSONP响应] 未找到对应的数据处理器', processorName);
                return;
            }

            // 处理数据
            const parsedData = processor(data);
            
            // 通过postMessage发送数据
            window.postMessage({
                type: 'ITEM_DATA',
                response: parsedData,
                channel: channel,
                source: url
            }, '*');
            
            logDebug(`[${channel}渠道] 数据已发送`, {
                channel: channel,
                itemCount: parsedData.length,
                source: url
            });
            
        } catch (e) {
            logDebug('[JSONP响应处理错误]', e);
        }
    }

    // ==================== 回调监控 ====================
    
    // 监控JSONP回调函数（通用版本）
    function monitorCallback(callbackName, url = '') {
        if (!callbackName || processedCallbacks.has(callbackName)) return;

        if (typeof window[callbackName] === 'function') {
            processedCallbacks.add(callbackName);

            // 保存原始函数
            const originalCallback = window[callbackName];

            // 替换为增强版本
            window[callbackName] = function (data) {
                try {
                    // 检查是否是目标API
                    const isTargetApi = TARGET_APIS.some(api => {
                        // 通过callback名称或URL判断
                        return (url && url.includes(api.url)) || 
                               (data.api && data.api.includes(api.url));
                    });

                    if (isTargetApi) {
                        logDebug(`[JSONP回调执行] ${callbackName}`, {
                            api: data.api,
                            url: url
                        });
                        processJsonpResponse(data, url || data.api || '');
                    }
                } catch (e) {
                    logDebug('[回调处理错误]', e);
                }

                // 调用原始回调
                return originalCallback.apply(this, arguments);
            };
        }
    }

    // 定期扫描新的jQuery回调函数
    setInterval(function () {
        for (const key in window) {
            if (
                (key.startsWith('jQuery') || key.startsWith('mtopjsonp')) &&
                typeof window[key] === 'function' &&
                !processedCallbacks.has(key)
            ) {
                monitorCallback(key);
            }
        }
    }, 500);

    // ==================== URL和回调名提取 ====================
    
    // 获取URL中的回调函数名
    function getCallbackName(url) {
        if (!url || typeof url !== 'string') return '';

        try {
            // 正则表达式查找callback参数
            const callbackMatch = url.match(/[?&](callback|jsonp|cb)=([^&]+)/);
            if (callbackMatch && callbackMatch[2]) {
                return decodeURIComponent(callbackMatch[2]);
            }
            
            // 检查特定的格式
            TARGET_APIS.forEach(api => {
                if (url.includes(api.url)) {
                    // 搜索渠道的jQuery格式
                    if (api.channel === CHANNELS.SEARCH) {
                        const mtopMatch = url.match(/jQuery(\d+)/);
                        if (mtopMatch) {
                            return `jQuery${mtopMatch[1]}`;
                        }
                    }
                    // 全部商品渠道的mtopjsonp格式
                    else if (api.channel === CHANNELS.ALL_PRODUCTS) {
                        const mtopMatch = url.match(/mtopjsonp(\d+)/);
                        if (mtopMatch) {
                            return `mtopjsonp${mtopMatch[1]}`;
                        }
                    }
                }
            });
        } catch (e) {
            logDebug('[回调名提取错误]', e);
        }

        return '';
    }

    // ==================== DOM监控 ====================
    
    // 使用MutationObserver监控DOM变化
    const observer = new MutationObserver(function (mutations) {
        mutations.forEach(function (mutation) {
            if (mutation.type === 'childList') {
                mutation.addedNodes.forEach(function (node) {
                    // 监控新增的script标签
                    if (node.tagName === 'SCRIPT' && node.src) {
                        const scriptUrl = node.src;
                        
                        // 检查是否是目标API
                        const targetApi = TARGET_APIS.find(api => scriptUrl.includes(api.url));
                        
                        if (targetApi && !processedUrls.has(scriptUrl)) {
                            processedUrls.add(scriptUrl);
                            logDebug(`[发现${targetApi.channel}渠道Script]`, scriptUrl);

                            const callbackName = getCallbackName(scriptUrl);
                            if (callbackName) {
                                // 立即尝试监控
                                monitorCallback(callbackName, scriptUrl);

                                // 延迟监控（有时回调函数在脚本加载后才创建）
                                setTimeout(() => monitorCallback(callbackName, scriptUrl), 100);
                            }

                            // 监控脚本加载完成事件
                            node.addEventListener('load', function () {
                                logDebug('[目标脚本加载完成]', scriptUrl);

                                // 脚本加载后再次尝试监控回调
                                if (callbackName) {
                                    monitorCallback(callbackName, scriptUrl);
                                }
                            });
                        }
                    }
                });
            }
        });
    });
    
    // 配置观察器
    observer.observe(document, {
        childList: true,
        subtree: true
    });

    // ==================== 初始化 ====================
    
    // 扫描已经存在的脚本标签
    function scanExistingScripts() {
        logDebug('[初始化] 扫描已存在的JSONP脚本标签');
        const scripts = document.querySelectorAll('script[src]');
        scripts.forEach(script => {
            const scriptUrl = script.src;
            if (scriptUrl) {
                // 检查是否是目标API
                const targetApi = TARGET_APIS.find(api => scriptUrl.includes(api.url));
                
                if (targetApi && !processedUrls.has(scriptUrl)) {
                    processedUrls.add(scriptUrl);
                    logDebug(`[发现已存在的${targetApi.channel}渠道Script]`, scriptUrl);

                    const callbackName = getCallbackName(scriptUrl);
                    if (callbackName) {
                        monitorCallback(callbackName, scriptUrl);
                    }
                }
            }
        });
    }

    // 在初始化时扫描现有脚本和回调
    scanExistingScripts();

    logDebug('[多渠道商品监控] 初始化完成，支持的渠道:', Object.values(CHANNELS));
})();