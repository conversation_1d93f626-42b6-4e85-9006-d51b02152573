<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>热浪联盟采集-控制面板</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    
    <style>
        /* Modern design system with refined colors */
        :root {
            --panel-bg: #f9f7f4;
            --panel-border: #e8e0d8;
            --panel-title-bg: #f3ece0;
            --text-primary: #2c2c2c;
            --text-secondary: #5a5a5a;
            --success-color: #27ae60;
            --toggle-bg: #f5f5f7;
            --toggle-active: #27ae60;
            --btn-export: #2680eb;
            --btn-export-hover: #1a73e8;
            --btn-clear: #ff5252;
            --btn-clear-hover: #ff3838;
            --panel-shadow: rgba(50, 50, 93, 0.1);
            --panel-width: 280px;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text", "Helvetica Neue", Arial, sans-serif;
            margin: 0;
            padding: 0;
        }

        /* Control panel container */
        #draggableDiv {
            position: fixed;
            right: 30px;
            top: 50%;
            transform: translateY(-50%);
            z-index: 2147483647;
            width: var(--panel-width);
        }

        .panel-container {
            background-color: var(--panel-bg);
            border-radius: 16px;
            box-shadow: 0 8px 30px var(--panel-shadow), 0 0 1px rgba(0, 0, 0, 0.08);
            overflow: hidden;
            transition: all 0.3s ease;
            border: 1px solid var(--panel-border);
        }

        /* Panel header */
        .panel-header {
            padding: 18px 20px 16px 20px;
            text-align: center;
            background-color: var(--panel-title-bg);
            border-bottom: 1px solid var(--panel-border);
            cursor: move;
        }

        .panel-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0;
        }

        /* Panel content */
        .panel-content {
            padding: 24px;
        }

        /* Collection status */
        .collection-status {
            text-align: center;
            margin-bottom: 24px;
            font-size: 20px;
            font-weight: 500;
            color: var(--success-color);
        }

        /* Toggle section */
        .toggle-section {
            display: flex;
            align-items: center;
            justify-content: space-between;
            background-color: #f1edeb;
            padding: 16px;
            border-radius: 14px;
            margin-bottom: 24px;
            border: 1px solid var(--panel-border);
        }

        .toggle-label {
            font-size: 16px;
            font-weight: 500;
            color: var(--text-primary);
        }

        /* Toggle switch */
        .toggle-switch {
            position: relative;
            width: 52px;
            height: 32px;
        }

        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .toggle-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #e0e0e0;
            border-radius: 32px;
            transition: .3s;
        }

        .toggle-slider:before {
            position: absolute;
            content: "";
            height: 28px;
            width: 28px;
            left: 2px;
            bottom: 2px;
            background-color: white;
            border-radius: 50%;
            transition: .3s;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        input:checked + .toggle-slider {
            background-color: var(--toggle-active);
        }

        input:checked + .toggle-slider:before {
            transform: translateX(20px);
        }

        /* Button group */
        .button-group {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 14px;
            margin-top: 8px;
        }

        .button {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 14px 0;
            border-radius: 14px;
            font-weight: 500;
            font-size: 15px;
            cursor: pointer;
            transition: all 0.2s;
            border: none;
            color: white;
        }

        .export-button {
            background-color: var(--btn-export);
        }

        .export-button:hover {
            background-color: var(--btn-export-hover);
        }

        .clear-button {
            background-color: var(--btn-clear);
        }

        .clear-button:hover {
            background-color: var(--btn-clear-hover);
        }

        .button-icon {
            width: 16px;
            height: 16px;
            margin-right: 8px;
        }
        
        /* Topics list */
        .topics-list {
            max-height: 200px;
            overflow-y: auto;
            padding: 0;
            margin: 0 0 20px 0;
            list-style-type: none;
        }
        
        /* Custom scrollbar */
        .topics-list::-webkit-scrollbar {
            width: 6px;
        }
        
        .topics-list::-webkit-scrollbar-track {
            background: var(--toggle-bg);
            border-radius: 4px;
        }
        
        .topics-list::-webkit-scrollbar-thumb {
            background-color: #c1c1c4;
            border-radius: 4px;
        }
        
        .topics-list::-webkit-scrollbar-thumb:hover {
            background-color: #a1a1a6;
        }

        /* 添加 switch-container 的样式 */
        .switch-container {
            display: flex;
            align-items: center;
            margin-right: 10px;
        }

        /* 添加开关的基本样式 */
        .switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }

        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 24px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 20px;
            width: 20px;
            left: 2px;
            bottom: 2px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background-color: var(--toggle-active);
        }

        input:checked + .slider:before {
            transform: translateX(26px);
        }

        #switch-label {
            font-size: 15px;
            color: var(--text-primary);
            margin-left: 10px;
        }

        /* 添加扩展特定的开关样式，确保在直播页面上正常显示 */
        .slider-extension {
            position: absolute !important;
            cursor: pointer !important;
            top: 0 !important;
            left: 0 !important;
            right: 0 !important;
            bottom: 0 !important;
            background-color: #ccc !important;
            transition: .4s !important;
            z-index: 9999 !important; /* 确保显示在页面元素之上 */
        }
        
        .slider-extension:before {
            position: absolute !important;
            content: "" !important;
            height: 20px !important;
            width: 20px !important;
            left: 2px !important;
            bottom: 2px !important;
            background-color: white !important;
            transition: .4s !important;
            border-radius: 50% !important;
        }
        
        input:checked + .slider-extension {
            background-color: var(--toggle-active) !important;
        }
        
        input:checked + .slider-extension:before {
            transform: translateX(26px) !important;
        }
        
        .round-extension {
            border-radius: 24px !important;
        }

        /* 添加全新的完全独立的开关样式，避免与网站样式冲突 */
        .taobao-extension-switch {
            position: relative !important;
            display: inline-block !important;
            width: 50px !important;
            height: 24px !important;
            margin: 0 !important;
            padding: 0 !important;
            z-index: 2147483646 !important;
            overflow: visible !important;
        }

        .taobao-extension-switch input {
            opacity: 0 !important;
            width: 0 !important;
            height: 0 !important;
            margin: 0 !important;
            padding: 0 !important;
        }

        .taobao-extension-slider {
            position: absolute !important;
            cursor: pointer !important;
            top: 0 !important;
            left: 0 !important;
            right: 0 !important;
            bottom: 0 !important;
            background-color: #ccc !important;
            transition: .4s !important;
            border-radius: 24px !important;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
            z-index: 2147483646 !important;
        }

        .taobao-extension-slider:before {
            position: absolute !important;
            content: "" !important;
            height: 20px !important;
            width: 20px !important;
            left: 2px !important;
            bottom: 2px !important;
            background-color: white !important;
            transition: .4s !important;
            border-radius: 50% !important;
            z-index: 2147483647 !important;
        }

        input:checked + .taobao-extension-slider {
            background-color: #27ae60 !important;
        }

        input:checked + .taobao-extension-slider:before {
            transform: translateX(26px) !important;
        }
    </style>
</head>
<body>
    <div id="draggableDiv">
        <div class="panel-container">
            <div class="panel-header">
                <h2 class="panel-title">热浪联盟采集-控制面板</h2>
            </div>
            
            <div class="panel-content">
                <div class="collection-status" id="data-num">
                    成功采集: 0
                </div>
                <div class="toggle-section">
                    <span class="toggle-label">佣金</span>
                    <div style="display: flex; align-items: center;">
                        <input type="number" id="min-commission" placeholder="" 
                               style="width: 60px; padding: 6px; border-radius: 8px; border: 1px solid var(--panel-border); margin-right: 5px; text-align: center;"
                               min="0" step="0.01">
                        <span style="margin: 0 5px;">-</span>
                        <input type="number" id="max-commission" placeholder="" 
                               style="width: 60px; padding: 6px; border-radius: 8px; border: 1px solid var(--panel-border); text-align: center;"
                               min="0" step="0.01">
                        <span style="margin-left: 5px;">元</span>
                    </div>
                </div>
                <div class="toggle-section">
                    <span id="switch-label">自动</span>
                    <div class="switch-container">
                        <label class="taobao-extension-switch">
                            <input type="checkbox" id="automatic-switch">
                            <div class="taobao-extension-slider"></div>
                        </label>
                    </div>
                </div>
               
                <div class="button-group">
                   
                    <button id="export-button" class="button export-button" style="padding: 10px 0; font-size: 13px;">
                        <svg class="button-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" style="width: 14px; height: 14px;">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                        </svg>
                        导出数据
                    </button>
                    <button id="remove-button" class="button clear-button" style="padding: 10px 0; font-size: 13px;">
                        <svg class="button-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" style="width: 14px; height: 14px;">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                        </svg>
                        清空数据
                    </button>
                </div>
            </div>
        </div>
    </div>

</body>
</html>