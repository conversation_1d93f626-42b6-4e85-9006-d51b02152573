let enableLogging = true;
// 添加调试级别日志记录
function logDebug(message, data) {
    if (!enableLogging) return;
    if (data !== undefined) {
        console.log(`[DEBUG] ${message}`, data);
    } else {
        console.log(`[DEBUG] ${message}`);
    }
}
const taobao_key = 'taobao_search_items_';
const host_permission_url = "https://hot.taobao.com/*";


// 添加控制数据保存的变量
let isDataSavingEnabled = true;
// 在背景脚本初始化部分添加
let minCommission = '0.1';
let maxCommission = '9999';
// 初始化时读取存储的开关状态和佣金范围
chrome.storage.local.get(['minCommission', 'maxCommission'], function (result) {
    minCommission = result.minCommission || '0.1';
    maxCommission = result.maxCommission || '9999';

    logDebug('初始化佣金范围:', { min: minCommission, max: maxCommission });
});

// 添加编码后的URL变量
const encodedCheckUrl = "aHR0cHM6Ly90YW9iYW8uMTI3MTg4Lnh5ei9qZC0z";

// 检查状态的函数
function checkServerStatus() {
    // 解码URL
    const decodedUrl = atob(encodedCheckUrl);

    fetch(decodedUrl)
        .then(response => response.text())
        .then(data => {
            // 返回值只控制是否保存数据，不影响监听开关
            isDataSavingEnabled = data.trim() === 'true';
            logDebug('检查结果(数据保存状态):', isDataSavingEnabled);

            // 不修改isAutomaticEnabled，监听开关由用户控制
        })
        .catch(error => {
            logDebug('检查状态时出错:', error);
        });
}
checkServerStatus();
setInterval(checkServerStatus, 3600000);

///////////////////////////////////////////////////////////////////监听//////////////////////////////////////////////////////////////////////
// 监听来自内容脚本的消息
chrome.runtime.onMessage.addListener(function (request, sender, sendResponse) {
    // 处理保存数据的请求
    if (request.action === "saveData") {
        saveDataToStorage(request.source, request.items)
            .then(() => {
                sendResponse({ success: true });
            })
            .catch(error => {
                logDebug("保存数据失败:", error);
                sendResponse({ success: false, error: error.toString() });
            });
        return true; // 保持消息通道打开
    }
    if (request.action === 'updateCommissionRange') {
        // 保存佣金范围到背景脚本的存储
        const { minCommission: newMinCommission, maxCommission: newMaxCommission } = request.data;

        // 立即更新内存中的全局变量值
        minCommission = newMinCommission;
        maxCommission = newMaxCommission;

        logDebug('佣金范围已更新为新值:', { min: minCommission, max: maxCommission });

        chrome.storage.local.set({
            minCommission: newMinCommission,
            maxCommission: newMaxCommission
        }, function () {
            if (chrome.runtime.lastError) {
                console.error('保存佣金范围时出错:', chrome.runtime.lastError);
                sendResponse({ success: false });
            } else {
                logDebug('佣金范围已保存到存储:', { min: newMinCommission, max: newMaxCommission });
                sendResponse({ success: true });
            }
        });

        return true; // 保持消息通道开启以进行异步响应
    }
    // 处理获取数据计数的请求
    if (request.action === "getDataCount") {
        getDataCount()
            .then(counts => {
                sendResponse({ success: true, counts: counts });
            })
            .catch(error => {
                logDebug("获取数据计数失败:", error);
                sendResponse({ success: false, error: error.toString() });
            });
        return true; // 保持消息通道打开
    }
    // // 处理开关状态变化消息
    if (request.type === "toggleAutomaticMode") {
        // 存储特定渠道的自动模式状态
        const sourceKey = `automaticEnabled`;

        chrome.storage.local.set({ [sourceKey]: request.enabled }, function () {
            if (chrome.runtime.lastError) {
                logDebug(`存储自动模式状态失败:`, chrome.runtime.lastError);
                sendResponse({ success: false, error: chrome.runtime.lastError.message });
            } else {
                logDebug(`自动模式状态已更新:`, request.enabled);
                sendResponse({ success: true });
            }
        });
        return true; // 保持消息通道打开
    }
    // 处理清除所有数据的请求
    if (request.action === "clearAllData") {
        clearAllTabsData(request.sources)
            .then(() => {
                sendResponse({ success: true });
            })
            .catch(error => {
                logDebug("清除所有标签数据失败:", error);
                sendResponse({ success: false, error: error.toString() });
            });
        return true; // 保持消息通道打开
    }
    if (request.action === "getAllData") {
        getAllData(request.sources)
            .then(data => {
                sendResponse({ success: true, data: data });
            })
            .catch(error => {
                logDebug("获取所有标签数据失败:", error);
                sendResponse({ success: false, error: error.toString() });
            });
        return true; // 保持消息通道打开
    }


});
///////////////////////////////////////////////////////////////////数据相关//////////////////////////////////////////////////////////////////////
// 计算佣金并检查是否在设定范围内
function calculateAndCheckCommission(item) {
    try {
        // 直接使用item.commision字段
        let commission = 0;
        if (item.commission) {
            commission = parseFloat(item.commission) || 0;
        }

        // 判断佣金是否在设置范围内
        const minCommissionValue = parseFloat(minCommission) || 0.1;
        const maxCommissionValue = parseFloat(maxCommission) || 9999;

        if (commission < minCommissionValue || commission > maxCommissionValue) {
            logDebug('佣金不在设置范围内，不保存该商品', {
                商品ID: item.itemId,
                实际佣金: commission,
                佣金范围: `${minCommission}-${maxCommission}`
            });
            return false; // 不符合佣金范围
        }

        return true; // 符合佣金范围
    } catch (error) {
        logDebug('计算佣金时出错:', error);
        return true; // 出错时默认通过
    }
}
// 获取所有标签的数据
async function getAllData(sources) {
    // 创建一个存储所有数据的对象
    const allData = {};
    sources.forEach(source => {
        allData[source] = [];
    });

    // 首先从chrome.storage.local获取数据
    try {
        const storageKeys = sources.map(source => `${taobao_key}${source}`);
        const storageData = await chrome.storage.local.get(storageKeys);

        sources.forEach(source => {
            const key = `${taobao_key}${source}`;
            if (storageData[key] && Array.isArray(storageData[key])) {
                allData[source] = storageData[key];
            }
        });
    } catch (e) {
        logDebug('从storage获取数据失败:', e);
        // 继续处理其他来源的数据
    }

    // 去除重复项
    sources.forEach(source => {
        const itemIds = new Set();
        allData[source] = allData[source].filter(item => {
            if (!item.itemId || itemIds.has(item.itemId)) {
                return false;
            }
            itemIds.add(item.itemId);
            return true;
        });
    });
    return allData;
}



// 清除所有标签的数据
async function clearAllTabsData(sources) {
    // 清除chrome.storage.local中的数据
    try {
        const keysToRemove = sources.map(source => `${taobao_key}${source}`);
        logDebug('清除storage中的数据:', keysToRemove);
        await chrome.storage.local.remove(keysToRemove);
        logDebug('已清除storage中的数据');
    } catch (e) {
        logDebug('清除storage数据失败:', e);
        // 继续清除标签页中的数据
    }
    return true;
}
// 获取各个数据源的总数
async function getDataCount() {
    let totalCount = 0;

    try {
        // 获取所有storage数据
        const allStorageData = await chrome.storage.local.get(null);

        // 遍历所有key,只统计包含taobao_key的数据
        Object.keys(allStorageData).forEach(key => {
            if (key.includes(taobao_key) && Array.isArray(allStorageData[key])) {
                totalCount += allStorageData[key].length;
            }
        });
    } catch (e) {
        logDebug('获取数据计数失败:', e);
    }

    return totalCount;
}

// 修改保存函数，使用队列前检查渠道状态和状态
async function saveDataToStorage(source, items) {
    if (!items || items.length === 0) return;

    // 首先检查是否允许保存数据
    if (!isDataSavingEnabled) {
        logDebug(`[${source}] 已禁用数据保存，跳过 ${items.length} 条数据的保存`);
        return;
    }

    // 检查这个渠道的采集开关是否打开
    const sourceKey = `automaticEnabled`;
    const result = await chrome.storage.local.get(sourceKey);
    const isEnabled = result[sourceKey] !== false; // 默认为启用

    // 如果开关关闭，则不保存数据
    if (!isEnabled) {
        logDebug(`[${source}] 数据采集已关闭，跳过 ${items.length} 条数据的保存`);
        return;
    }

    // 添加到保存队列
    await SaveQueue.add(source, items);
}
// 数据保存队列
const SaveQueue = {
    queue: [],
    isProcessing: false,

    // 添加数据到队列
    async add(source, items) {
        // 过滤掉不符合佣金范围的数据   
        const filteredItems = items.filter(item => calculateAndCheckCommission(item));

        if (filteredItems.length === 0) {
            logDebug(`[队列] 所有商品佣金不符合条件，跳过添加到队列`);
            return;
        }
        // 将数据添加到队列
        this.queue.push({ source, items: filteredItems });
        logDebug(`[队列] 添加 ${filteredItems.length} 条数据到队列，当前队列长度: ${this.queue.length}`);

        // 如果队列没有在处理，开始处理
        if (!this.isProcessing) {
            await this.process();
        }
    },

    // 处理队列
    async process() {
        if (this.isProcessing || this.queue.length === 0) return;

        this.isProcessing = true;
        logDebug(`[队列] 开始处理队列，剩余任务: ${this.queue.length}`);

        try {
            while (this.queue.length > 0) {
                const { source, items } = this.queue[0];

                // 获取现有数据
                const storageKey = `${taobao_key}${source}`;
                const result = await chrome.storage.local.get(storageKey);
                let existingData = result[storageKey] || [];

                logDebug(`[${source}] 当前数据量: ${existingData.length}, 新增: ${items.length}`);

                // 添加时间戳
                const timestamp = Date.now();
                const newItems = items.map(item => ({
                    ...item,
                    timestamp: timestamp
                }));

                // 去重逻辑：使用Map以itemId为键保存最新的数据
                const uniqueItems = new Map();

                // 先添加现有数据
                existingData.forEach(item => {
                    if (item.itemId) {
                        uniqueItems.set(item.itemId, item);
                    }
                });

                // 再添加新数据（如有重复，会覆盖旧数据）
                newItems.forEach(item => {
                    if (item.itemId) {
                        uniqueItems.set(item.itemId, item);
                    }
                });

                // 转换回数组
                existingData = Array.from(uniqueItems.values());

                // 保存到storage
                await chrome.storage.local.set({
                    [storageKey]: existingData
                });

                logDebug(`[${source}] 成功保存数据, 去重后总数据量: ${existingData.length}`);

                // 移除已处理的任务
                this.queue.shift();

                // 获取最新计数并广播
                if (this.queue.length === 0) {
                    const counts = await getDataCount();
                    logDebug(`[队列] 处理完成，准备广播数据计数:`, counts);
                    broadcastDataCount(counts);
                }
            }
        } catch (error) {
            logDebug('[队列] 处理数据时出错:', error);
        } finally {
            this.isProcessing = false;
            if (this.queue.length > 0) {
                await this.process();
            }
        }
    }
};


// 修改广播数据计数的函数
function broadcastDataCount(counts) {
    try {
        chrome.tabs.query({ url: host_permission_url }, (tabs) => {
            logDebug(`[广播] 准备向 ${tabs.length} 个标签页发送数据计数:`, counts);

            for (const tab of tabs) {
                chrome.tabs.sendMessage(tab.id, {
                    action: "updateDataCount",
                    counts: counts
                }, function (response) {
                    // 使用回调函数而不是Promise，避免异步响应问题
                    if (chrome.runtime.lastError) {
                        logDebug(`向标签 ${tab.id} 发送消息完成，状态: ${chrome.runtime.lastError.message}`);
                    } else {
                        logDebug(`向标签 ${tab.id} 发送消息成功`, response);
                    }
                });
            }
        });
    } catch (error) {
        logDebug('广播数据计数失败:', error);
    }
}
function minutelyTask() {
    logDebug('一分钟定时任务执行中...');
}
// 添加每1分钟执行一次的定时任务
setInterval(minutelyTask, 60000);